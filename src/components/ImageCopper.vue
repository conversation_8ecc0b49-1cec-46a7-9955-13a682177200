<template>
  <a-modal
    class="image-cropper"
    v-model:visible="visible"
    title="编辑图片"
    :footer="false"
    @cancel="closeModal"
  >
    <vue-cropper
      ref="cropperRef"
      :img="imageUrl"
      :autoCrop="true"
      :fixedBox="false"
      :centerBox="true"
      :canMoveBox="true"
      :info="true"
      outputType="png"
    />
    <div style="margin-bottom: 16px" />
    <!-- 图片操作 -->
    <div class="image-cropper-actions">
      <a-tabs v-model:activeKey="operation">
        <a-tab-pane key="cut" tab="裁切">
          <div class="operationContainer">
            <a-button @click="rotateLeft">向左旋转</a-button>
            <a-button @click="rotateRight">向右旋转</a-button>
          </div>
        </a-tab-pane>
        <a-tab-pane key="reposition" tab="重构" force-render>
          <div class="operationContainer">
            <a-button @click="changeScale(1)">放大</a-button>
            <a-button @click="changeScale(-1)">缩小</a-button>
            <div class="aspect-ratio-selector">
              <a-radio-group v-model:value="aspectRatio" button-style="solid">
                <a-radio-button value="free">自由比例</a-radio-button>
                <a-radio-button value="1:1">1:1</a-radio-button>
                <a-radio-button value="4:3">4:3</a-radio-button>
                <a-radio-button value="16:9">16:9</a-radio-button>
                <a-radio-button value="3:4">3:4</a-radio-button>
                <a-radio-button value="9:16">9:16</a-radio-button>
              </a-radio-group>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
      <a-space>
        <a-button type="primary" id="confirmButton" :loading="loading" @click="handleConfirm"
          >确认</a-button
        >
      </a-space>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { blob } from 'stream/consumers'
import { uploadPictureUsingPost } from '@/api/pictureController'
import { message } from 'ant-design-vue'
interface Props {
  imageUrl?: string
  picture?: API.PictureVO
  onSuccess?: (newPicture: API.PictureVO) => void
}

const props = defineProps<Props>()
const loading = ref(false)
// 编辑器组件的引用
const cropperRef = ref()
const visible = ref(true)
// 是否可见

// 打开弹窗
const openModal = () => {
  visible.value = true
}

// 关闭弹窗
const closeModal = () => {
  visible.value = false
}
const operation = ref<'cut' | 'reposition'>('reposition')
// 暴露函数给父组件
defineExpose({
  openModal,
})

// 向左旋转
const rotateLeft = () => {
  cropperRef.value.rotateLeft()
}

// 向右旋转
const rotateRight = () => {
  cropperRef.value.rotateRight()
}

// 缩放
const changeScale = (num: number) => {
  cropperRef.value.changeScale(num)
}

const handleConfirm = () => {
  cropperRef.value.getCropBlob((blob: Blob) => {
    const fileName = (props.picture?.name || 'image') + '.png'
    const file = new File([blob], fileName, { type: blob.type })
    handleUpload({ file })
  })
}

const handleUpload = async ({ file }: any) => {
  loading.value = true
  try {
    const params = props.picture ? { id: props.picture.id } : {}
    const res = await uploadPictureUsingPost(params, {}, file)
    if (res.data.code == 0 && res.data.data) {
      message.success('图片上传成功')
      props.onSuccess?.(res.data.data)
    } else {
      message.error('图片上传失败' + res.data.message)
    }
  } catch (error) {
    message.error('图片上传失败' + error.message)
    console.error('图片上传失败:', error)
  }
  loading.value = false
}
</script>

<style scoped>
.image-cropper {
  text-align: center;
}

.image-cropper .vue-cropper {
  height: 400px;
}

.image-cropper .operationContainer {
  text-align: center;
}

.image-cropper #confirmButton {
  text-align: right;
}
</style>
