<template>
  <div id="addPicture">
    <h2 style="margin-bottom: 16px">
      {{ route.query.id ? '编辑图片' : '添加图片' }}
    </h2>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="file" tab="文件上传"
        ><PictureUpload :picture="picture" :onSuccess="onSuccess"></PictureUpload
      ></a-tab-pane>
      <a-tab-pane key="url" tab="URL 上传" force-render>
        <UrlPictureUpload :picture="picture" :onSuccess="onSuccess"></UrlPictureUpload>
      </a-tab-pane>
    </a-tabs>
    <div v-if="picture" class="edit-bar">
      <a-button :icon="h(EditOutlined)" @click="doEditPicture">编辑图片</a-button>
      <ImageCropper
        ref="imageCropperRef"
        :imageUrl="picture.url"
        :picture="picture"
        :onSuccess="onCropSuccess"
      />
    </div>

    <!--表单-->
    <a-form v-if="picture" layout="vertical" :model="formState" @finish="handleUpload">
      <a-form-item label="图片标题" name="name">
        <a-input v-model:value="formState.name" placeholder="请输入图片标题" allow-clear />
      </a-form-item>

      <a-form-item label="图片简介" name="introduction">
        <a-textarea
          v-model:value="formState.introduction"
          placeholder="请输入图片简介"
          :auto-size="{ minRows: 2, maxRows: 5 }"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="图片分类" name="category">
        <a-auto-complete
          v-model:value="formState.category"
          placeholder="请输入图片分类"
          allow-clear
          :options="categoryList"
        />
      </a-form-item>
      <a-form-item label="图片标签" name="tags">
        <a-select
          v-model:value="formState.tags"
          placeholder="请输入图片标签"
          allow-clear
          mode="tags"
          :options="tagList"
        >
        </a-select>
      </a-form-item>

      <a-form-item>
        <a-button type="primary" html-type="submit" style="width: 100%">上传</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import {
  editPictureUsingPost,
  getPictureVoByIdUsingGet,
  listPictureTagCategoryUsingGet,
} from '@/api/pictureController'
import PictureUpload from '@/components/PictureUpload.vue'
import UrlPictureUpload from '@/components/UrlPictureUpload.vue'
import router from '@/router'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'

const picture = ref<API.PictureVO>()
const activeKey = ref<'file' | 'url'>('file')
const formState = reactive<API.PictureEditRequest>({})

const onSuccess = (newPicture: API.PictureVO) => {
  picture.value = newPicture
  formState.name = newPicture.name
}

const handleUpload = async (values: any) => {
  const pictureId = picture.value?.id
  if (!pictureId) {
    message.error('请先上传图片')
    return
  }
  const res = await editPictureUsingPost({ id: pictureId, ...values })
  if (res.data.code == 0 && res.data.data) {
    message.success('图片创建成功')
    router.push({
      path: `/picture/${pictureId}`,
    })
  } else {
    message.error('图片创建失败' + res.data.message)
  }
}

const categoryList = ref<string[]>([])
const tagList = ref<string[]>([])

const fetchCategoryAndTags = async () => {
  // 假设有 API 获取分类和标签
  const res = await listPictureTagCategoryUsingGet()
  if (res.data.code == 0 && res.data.data) {
    categoryList.value = (res.data.data.categoryList ?? []).map((value: string) => {
      return {
        value: value,
        label: value,
      }
    })

    tagList.value = (res.data.data.tagList ?? []).map((value: string) => {
      return {
        value: value,
        label: value,
      }
    })
  }
}

const route = useRoute()

const fetchOldPicture = async () => {
  const pictureId = route.query.id
  if (pictureId) {
    const res = await getPictureVoByIdUsingGet({ id: pictureId })
    if (res.data.code == 0 && res.data.data) {
      picture.value = res.data.data
      formState.name = res.data.data.name
      formState.introduction = res.data.data.introduction
      formState.category = res.data.data.category
      formState.tags = res.data.data.tags
    } else {
      message.error('获取图片信息失败' + res.data.message)
    }
  }
}
// 图片编辑弹窗引用
const imageCropperRef = ref()

// 编辑图片
const doEditPicture = () => {
  if (imageCropperRef.value) {
    imageCropperRef.value.openModal()
  }
}

// 编辑成功事件
const onCropSuccess = (newPicture: API.PictureVO) => {
  picture.value = newPicture
}

onMounted(() => {
  fetchCategoryAndTags()
  fetchOldPicture()
})
</script>

<style scoped>
#addPicture {
  max-width: 720px;
  margin: 0 auto;
}
</style>
