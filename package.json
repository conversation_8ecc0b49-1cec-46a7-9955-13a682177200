{"name": "c-picture-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "openapi": "node openapi.config.js"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.10.0", "file-saver": "^2.0.5", "lazysizes": "^5.3.2", "pinia": "^2.2.6", "tslib": "^2.8.1", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-router": "^4.4.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/file-saver": "^2.0.7", "@types/node": "^22.9.3", "@umijs/openapi": "^1.13.15", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.1", "prettier": "^3.3.3", "typescript": "~5.6.3", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vue-tsc": "^2.1.10"}}